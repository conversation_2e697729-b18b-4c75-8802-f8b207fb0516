<?php

return [
    // Order Status Constants
    'ORDER_STATUSES' => [
        'Pending' => 'Pending',
        'Processing' => 'Processing',
        'Shipped' => 'Shipped',
        'Partially Delivered' => 'Partially Delivered',
        'Delivered' => 'Delivered',
        'Cancelled' => 'Cancelled',
        'Returned' => 'Returned',
        'Pending Cancellation' => 'Pending Cancellation',
        'Cancellation Rejected' => 'Cancellation Rejected',
        'Pending Return' => 'Pending Return',
        'Return Approved' => 'Return Approved',
        'Return Rejected' => 'Return Rejected',
        'Expired' => 'Expired',
        'Deleted' => 'Deleted'
    ],


    // Shipping Methods Constants
    'SHIPPING_METHODS' => [
        'express' => 'Express',
        'standard' => 'Standard'
    ],

    // Payment Status Constants
    'PAYMENT_STATUSES' => [
        'Pending' => 'Pending',
        /* 'Completed' => 'Completed',*/
        'Failed' => 'Failed',
        'Refunded' => 'Refunded',
        'Paid' => 'Paid',
        'Unpaid' => 'Unpaid'
    ],

    'ORDER_TYPE_STATUSES' => [
        'Online' => 'Online',
        'Showroom' => 'Showroom'
    ],

    // Redeem Mode Constants
    'REDEEM_MODE' => [
        'Online' => 'Online',
        'Store' => 'Store',
        'Both' => 'Both'
    ],

    // Redeem Mode Constants
    'REDEEM_MODE' => [
        'Online' => 'Online',
        'Store' => 'Store',
        'Both' => 'Both'
    ],

    // Offer Type Constants
    'OFFER_TYPE' => [
        'Flat' => 'Flat',
        'Percentage' => 'Percentage'
    ],

    // Customer Group - Temporary, will create table later
    'CUSTOMER_GROUP' => [
        'group1' => 'Customer Group 1',
        'group2' => 'Customer Group 2',
        'group3' => 'Customer Group 3',
        'group4' => 'Customer Group 4',
        'group5' => 'Customer Group 5'
    ],

    // Records status in each table without delete
    'STATUS' => [
        'A' => 'Active',
        'I' => 'Inactive',
        //'D' => 'Deleted'
    ],

    // Records status in each table with delete
    'STATUS_ALL' => [
        'A' => 'Active',
        'I' => 'Inactive',
        'D' => 'Deleted'
    ],

    //Status Map color code
    'STATUS_MAP' => [
        'A' => ['label' => 'Active', 'class' => 'col-green'],
        'I' => ['label' => 'Inactive', 'class' => 'col-red'],
        'D' => ['label' => 'Deleted', 'class' => 'col-red']
    ],

    'ORDER_STATUSES_MAP' => [
        'Pending' => ['label' => 'Pending', 'class' => 'col-orange'],
        'Processing' => ['label' => 'Processing', 'class' => 'col-purple'],
        'Shipped' => ['label' => 'Shipped', 'class' => 'col-blue'],
        'Partially Delivered' => ['label' => 'Partially Delivered', 'class' => 'col-teal'],
        'Delivered' => ['label' => 'Delivered', 'class' => 'col-green'],
        'Cancelled' => ['label' => 'Cancelled', 'class' => 'col-red'],
        'Returned' => ['label' => 'Returned', 'class' => 'col-red'],
        'Pending Cancellation' => ['label' => 'Pending Cancellation', 'class' => 'col-yellow'],
        'Cancellation Rejected' => ['label' => 'Cancellation Rejected', 'class' => 'col-yellow'],
        'Pending Return' => ['label' => 'Pending Return', 'class' => 'col-pink'],
        'Return Approved' => ['label' => 'Return Approved', 'class' => 'col-pink'],
        'Return Rejected' => ['label' => 'Return Rejected', 'class' => 'col-pink'],
        'Expired' => ['label' => 'Expired', 'class' => 'col-purple'],
        'Deleted' => ['label' => 'Deleted', 'class' => 'col-gray']
    ],

    'PAYMENT_STATUSES_MAP' => [
        'Pending' => ['label' => 'Pending', 'class' => 'col-orange'],
        /*'Completed' => ['label' => 'Completed', 'class' => 'col-green'],*/
        'Failed' => ['label' => 'Failed', 'class' => 'col-red'],
        'Refunded' => ['label' => 'Refunded', 'class' => 'col-blue'],
        'Paid' => ['label' => 'Paid', 'class' => 'col-green'],
        'Unpaid' => ['label' => 'Unpaid', 'class' => 'col-purple']
    ],

    //Coupon Code Length
    'COUPON_CODE_LENGTH' => 10,

    //File Paths
    'COUPON_WEB_IMAGE' => "coupons/web_image/",
    'COUPON_MOB_IMAGE' => "coupons/mob_image/",

    //Image dimensions
    'COUPON_WEB_IMAGE_MIN_WIDTH' => 240,
    'COUPON_WEB_IMAGE_MAX_WIDTH' => 320,
    'COUPON_WEB_IMAGE_MIN_HEIGHT' => 180,
    'COUPON_WEB_IMAGE_MAX_HEIGHT' => 250,

    'COUPON_MOB_IMAGE_MIN_WIDTH' => 170,
    'COUPON_MOB_IMAGE_MAX_WIDTH' => 220,
    'COUPON_MOB_IMAGE_MIN_HEIGHT' => 100,
    'COUPON_MOB_IMAGE_MAX_HEIGHT' => 140,

    //Allowed Image Types
    'COUPON_WEB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'COUPON_WEB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'COUPON_WEB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'COUPON_MOB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'COUPON_MOB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'COUPON_MOB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],

    //MAX Allowed Size pf Images in mb
    'COUPON_WEB_IMAGE_SIZE' => 10,
    'COUPON_MOB_IMAGE_SIZE' => 5,

    //CONTENT PAGE IMAGE DIMENSIONS
    'CONTENT_WEB_IMAGE_MIN_WIDTH' => 300,
    'CONTENT_WEB_IMAGE_MAX_WIDTH' => 400,
    'CONTENT_WEB_IMAGE_MIN_HEIGHT' => 200,
    'CONTENT_WEB_IMAGE_MAX_HEIGHT' => 320,
    //MAX ALLOWED SIZE IMAGES IN MB
    'CONTENT_WEB_IMAGE_SIZE' => 10,
    //TYPES
    'CONTENT_WEB_IMAGE_TYPE' => ".jpg, .jpeg, .png",

    //SHOWROOM IMAGES PATH, SIZE, TYPES, DIMENSIONS
    'SHOWROOM_IMAGE' => "showroom_image/",

    //SHOWROOOM IMAGE DIMENSIONS
    'SHOWROOM_IMAGE_MIN_WIDTH' => 300,
    'SHOWROOM_IMAGE_MAX_WIDTH' => 400,
    'SHOWROOM_IMAGE_MIN_HEIGHT' => 200,
    'SHOWROOM_IMAGE_MAX_HEIGHT' => 320,

    //SHOWROOM ALLOWED IMAGE TYPES
    'SHOWROOM_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'SHOWROOM_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'SHOWROOM_IMAGE_TEXT_TYPE' => ".jpg, .jpeg, .png",

    //MAX Allowed Size pf Images in mb
    'SHOWROOM_IMAGE_SIZE' => 10,


    //Banner type
    'BANNER_TYPE' => [
        'Image' => 'Image',
        'Video' => 'Video'
    ],

    //Banner Location
    'BANNER_LOCATION' => [
        'Home Page' => 'Home Page',
        'Product Category' => 'Product Category',
        'Brand Page' => 'Brand Page',
        'Deals Page' => 'Deals Page'
    ],

    'TARGET_MOB' => [
        'best_selling' => 'Best Selling',
        'new_arrival' => 'New Arrival',
        'deals_of_the_day' => 'Deals of the Day',
        'featured' => 'Featured',
        'products' => 'Products'
    ],

    //File Paths
    'BANNER_WEB_IMAGE' => "banners/web_media/",
    'BANNER_MOB_IMAGE' => "banners/mob_media/",

    //Image dimensions
    'BANNER_WEB_IMAGE_MIN_WIDTH' => 240,
    'BANNER_WEB_IMAGE_MAX_WIDTH' => 320,
    'BANNER_WEB_IMAGE_MIN_HEIGHT' => 180,
    'BANNER_WEB_IMAGE_MAX_HEIGHT' => 250,

    'BANNER_MOB_IMAGE_MIN_WIDTH' => 170,
    'BANNER_MOB_IMAGE_MAX_WIDTH' => 220,
    'BANNER_MOB_IMAGE_MIN_HEIGHT' => 100,
    'BANNER_MOB_IMAGE_MAX_HEIGHT' => 140,

    //Video dimensions
    'BANNER_WEB_VIDEO_MIN_WIDTH' => 640,
    'BANNER_WEB_VIDEO_MAX_WIDTH' => 3840,
    'BANNER_WEB_VIDEO_MIN_HEIGHT' => 360,
    'BANNER_WEB_VIDEO_MAX_HEIGHT' => 2160,

    'BANNER_MOB_VIDEO_MIN_WIDTH' => 640,
    'BANNER_MOB_VIDEO_MAX_WIDTH' => 3840,
    'BANNER_MOB_VIDEO_MIN_HEIGHT' => 360,
    'BANNER_MOB_VIDEO_MAX_HEIGHT' => 2160,

    //Allowed Image Types
    'BANNER_WEB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'BANNER_WEB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'BANNER_WEB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'BANNER_MOB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'BANNER_MOB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'BANNER_MOB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],

    //Allowed Video Types
    'BANNER_WEB_VIDEO_TYPE' => ["mp4", "avi", "mov", "wmv"],
    'BANNER_WEB_VIDEO_TYPE_DISP' => [".mp4", ".avi", ".mov", ".wmv"],
    'BANNER_WEB_VIDEO_JS_TYPE' => ['video/mp4', 'video/x-msvideo', 'video/quicktime', 'video/x-ms-wmv'],
    'BANNER_MOB_VIDEO_TYPE' => ["mp4", "avi", "mov", "wmv"],
    'BANNER_MOB_VIDEO_TYPE_DISP' => [".mp4", ".avi", ".mov", ".wmv"],
    'BANNER_MOB_VIDEO_JS_TYPE' => ['video/mp4', 'video/x-msvideo', 'video/quicktime', 'video/x-ms-wmv'],

    //MAX Allowed Size pf Images in mb
    'BANNER_WEB_IMAGE_SIZE' => 10,
    'BANNER_MOB_IMAGE_SIZE' => 5,

    //MAX Allowed Size pf Video in mb
    'BANNER_WEB_VIDEO_SIZE' => 50,
    'BANNER_MOB_VIDEO_SIZE' => 50,

    //BANNER View
    'BANNER_VIEW' => [
        'Web' => 'Web',
        'Mobile' => 'Mobile',
        'Both' => 'Both'
    ],

    'BRAND_LOGO_SIZE' => 5,
    'BRAND_LOGO_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'BRAND_LOGO_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'BRAND_LOGO_MIN_WIDTH' => 240,
    'BRAND_LOGO_MAX_WIDTH' => 320,
    'BRAND_LOGO_MIN_HEIGHT' => 180,
    'BRAND_LOGO_MAX_HEIGHT' => 250,

    'BRAND_BANNER_SIZE' => 5,
    'BRAND_BANNER_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'BRAND_BANNER_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'BRAND_BANNER_MIN_WIDTH' => 240,
    'BRAND_BANNER_MAX_WIDTH' => 320,
    'BRAND_BANNER_MIN_HEIGHT' => 180,
    'BRAND_BANNER_MAX_HEIGHT' => 250,

    'CATEGORY_ICON_SIZE' => 5,
    'CATEGORY_ICON_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'CATEGORY_ICON_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'CATEGORY_ICON_MIN_WIDTH' => 100,
    'CATEGORY_ICON_MAX_WIDTH' => 100,
    'CATEGORY_ICON_MIN_HEIGHT' => 100,
    'CATEGORY_ICON_MAX_HEIGHT' => 100,

    'CATEGORY_WEB_BANNER_SIZE' => 10,
    'CATEGORY_WEB_BANNER_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'CATEGORY_WEB_BANNER_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'CATEGORY_WEB_BANNER_MIN_WIDTH' => 300,
    'CATEGORY_WEB_BANNER_MAX_WIDTH' => 350,
    'CATEGORY_WEB_BANNER_MIN_HEIGHT' => 300,
    'CATEGORY_WEB_BANNER_MAX_HEIGHT' => 350,

    'CATEGORY_MOBILE_BANNER_SIZE' => 10,
    'CATEGORY_MOBILE_BANNER_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'CATEGORY_MOBILE_BANNER_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'CATEGORY_MOBILE_BANNER_MIN_WIDTH' => 300,
    'CATEGORY_MOBILE_BANNER_MAX_WIDTH' => 350,
    'CATEGORY_MOBILE_BANNER_MIN_HEIGHT' => 300,
    'CATEGORY_MOBILE_BANNER_MAX_HEIGHT' => 350,

    'PRODUCT_IMAGE_SIZE' => 10,
    'PRODUCT_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'PRODUCT_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'PRODUCT_VIDEO_TYPE_DISP' => [".mp4", ".avi", ".mov", ".wmv"],
    'PRODUCT_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'PRODUCT_IMAGE_MIN_WIDTH' => 150,
    'PRODUCT_IMAGE_MAX_WIDTH' => 225,
    'PRODUCT_IMAGE_MIN_HEIGHT' => 130,
    'PRODUCT_IMAGE_MAX_HEIGHT' => 225,

    // ORDER RETURN DEFECT IMAGES PATH 
    'ORDER_RETURN_DEFECT_IMAGE' => "order_return_defect_images/",

    // SUPPLIER PAYMENT CHEQUE COPY 
    'SUPPLIER_PAYMENT_CHEQUE_COPY' => "supplier_payment_cheque_copy/",

    //INCOMING STOCK TO WAREHOUSE DOCUMENT
    'INCOMING_STOCK_DOCUMENT' => "incoming_stock_document/",

    //INCOMING STOCK IMAGE DIMENSIONS
    'INCOMING_STOCK_DOCUMENT_MIN_WIDTH' => 300,
    'INCOMING_STOCK_DOCUMENT_MAX_WIDTH' => 400,
    'INCOMING_STOCK_DOCUMENT_MIN_HEIGHT' => 200,
    'INCOMING_STOCK_DOCUMENT_MAX_HEIGHT' => 320,

    //INCOMING STOCK ALLOWED IMAGE TYPES
    'INCOMING_STOCK_DOCUMENT_TYPE' => ["jpg", "jpeg", "png", "pdf", "doc", "docx"],
    'INCOMING_STOCK_DOCUMENT_JS_TYPE' => [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"],
    'INCOMING_STOCK_DOCUMENT_TEXT_TYPE' => ".jpg, .jpeg, .png, .pdf, .doc, .docx",
    'INCOMING_STOCK_DOCUMENT_IMAGE_TYPE' => ".jpg, .jpeg, .png, .pdf, .doc, .docx",

    //MAX Allowed Size pf Images in mb
    'INCOMING_STOCK_DOCUMENT_SIZE' => 10,

    //DRIVER IMAGES PATH, SIZE, TYPES, DIMENSIONS
    'DRIVER_IMAGE' => "driver_image/",

    //SHOWROOOM IMAGE DIMENSIONS
    'DRIVER_IMAGE_MIN_WIDTH' => 300,
    'DRIVER_IMAGE_MAX_WIDTH' => 400,
    'DRIVER_IMAGE_MIN_HEIGHT' => 200,
    'DRIVER_IMAGE_MAX_HEIGHT' => 320,

    //DRIVER ALLOWED IMAGE TYPES
    'DRIVER_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'DRIVER_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'DRIVER_IMAGE_TEXT_TYPE' => ".jpg, .jpeg, .png",


    //MAX Allowed Size pf Images in mb
    'DRIVER_IMAGE_SIZE' => 10,

    // added delete messages because of multilingual we are using and we are using js file for this so can't add __() in js so added like this if not clear then ask me
    'DELETE_CONFIRMATION_MESSAGE' => __('Are you sure?'),
    'DELETE_WARNING_MESSAGE' => __('Once deleted, you will not be able to recover this record!'),
    'DELETE_FAIL_MESSAGE' => __('Your record is safe!'),

    //Widget Types
    'WIDGET_TYPE' => [
        'Banner' => 'Banner',
        'Better Life' => 'Better Life',
        'Featured' => 'Featured',
        'Latest Offers' => 'Latest Offers',       
        'Best Seller' => 'Best Seller',
        'New Arrivals' => 'New Arrivals',
        'Deals of the Day' => 'Deals of the Day',
        'General Section' => 'General Section',
        'Customer Story' => 'Customer Story'
    ],

    //Product Preference
    'PRODUCT_PREFERENCE' => [
        'Best Selling' => 'Best Seller',
        'New Arrival' => 'New Arrivals',
        'Special Offers' => 'Special Offers',
        'Deal' => 'Deal of the Day'
    ],

    //Widget Checkboxes
    'WIDGET_DISPLAY_CHECKBOXES' => [
        [
            'label' => 'Web',
            'value' => 'web',
            'id'    => 'widget-web',
        ],
        [
            'label' => 'Mobile',
            'value' => 'mobile',
            'id'    => 'widget-mobile',
        ]
    ],

    //Banner Ads Types
    'BANNER_AD_TYPES' => [
        'Banner' => 'Banner',
        'Sidebar Left' => 'Sidebar Left',
        'Sidebar Right' => 'Sidebar Right',
        'Popup' => 'Popup',
        'Middle' => 'Middle',
        'Above Footer' => 'Above Footer'
    ],

    //Banner Ad Locations
    'BANNER_AD_LOC' => [
        'Home Page' => 'Home Page',
        'Product Category' => 'Product Category',
        'Product Page' => 'Product Page',
        'Brand Page' => 'Brand Page',
        'Deals Page' => 'Deals Page',
        'Checkout' => 'Checkout',
        'Order Success' => 'Order Success',
        'Login' => 'Login',
        'Signup' => 'Signup',
        'Forget' => 'Forget'
    ],

    //File Paths
    'ADBLOCK_WEB_IMAGE' => "adblock/web_image/",
    'ADBLOCK_MOB_IMAGE' => "adblock/mob_image/",

    //Image dimensions
    'ADBLOCK_WEB_IMAGE_MIN_WIDTH' => 240,
    'ADBLOCK_WEB_IMAGE_MAX_WIDTH' => 320,
    'ADBLOCK_WEB_IMAGE_MIN_HEIGHT' => 180,
    'ADBLOCK_WEB_IMAGE_MAX_HEIGHT' => 250,

    'ADBLOCK_MOB_IMAGE_MIN_WIDTH' => 170,
    'ADBLOCK_MOB_IMAGE_MAX_WIDTH' => 220,
    'ADBLOCK_MOB_IMAGE_MIN_HEIGHT' => 100,
    'ADBLOCK_MOB_IMAGE_MAX_HEIGHT' => 140,

    //Allowed Image Types
    'ADBLOCK_WEB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'ADBLOCK_WEB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'ADBLOCK_WEB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'ADBLOCK_MOB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'ADBLOCK_MOB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'ADBLOCK_MOB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],

    //MAX Allowed Size pf Images in mb
    'ADBLOCK_WEB_IMAGE_SIZE' => 10,
    'ADBLOCK_MOB_IMAGE_SIZE' => 5,

    //File Paths
    'WIDGET_WEB_IMAGE' => "widget/web_image/",
    'WIDGET_MOB_IMAGE' => "widget/mob_image/",

    //Image dimensions
    'WIDGET_WEB_IMAGE_MIN_WIDTH' => 240,
    'WIDGET_WEB_IMAGE_MAX_WIDTH' => 320,
    'WIDGET_WEB_IMAGE_MIN_HEIGHT' => 180,
    'WIDGET_WEB_IMAGE_MAX_HEIGHT' => 250,

    'WIDGET_MOB_IMAGE_MIN_WIDTH' => 170,
    'WIDGET_MOB_IMAGE_MAX_WIDTH' => 220,
    'WIDGET_MOB_IMAGE_MIN_HEIGHT' => 100,
    'WIDGET_MOB_IMAGE_MAX_HEIGHT' => 140,

    //Allowed Image Types
    'WIDGET_WEB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'WIDGET_WEB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'WIDGET_WEB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'WIDGET_MOB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'WIDGET_MOB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'WIDGET_MOB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],

    //MAX Allowed Size pf Images in mb
    'WIDGET_WEB_IMAGE_SIZE' => 10,
    'WIDGET_MOB_IMAGE_SIZE' => 5,

    //File Paths
    'PRODUCT_IMAGE' => "products/images/",
    'PRODUCT_VIDEO' => "products/video/",
    'PRODUCT_VARIANT_IMAGE' => "product_variants/images/",
    'PRODUCT_VARIANT_VIDEO' => "product_variants/videos/",

    //Video dimensions
    'PRODUCT_VIDEO_MIN_WIDTH' => 640,
    'PRODUCT_VIDEO_MAX_WIDTH' => 3840,
    'PRODUCT_VIDEO_MIN_HEIGHT' => 360,
    'PRODUCT_VIDEO_MAX_HEIGHT' => 2160,

    //Allowed Video Types
    'PRODUCT_VIDEO_TYPE' => ["mp4", "avi", "mov", "wmv"],
    'PRODUCT_VIDEO_JS_TYPE' => ['video/mp4', 'video/x-msvideo', 'video/quicktime', 'video/x-ms-wmv'],

    //MAX Allowed Size pf Video in mb
    'PRODUCT_VIDEO_SIZE' => 50,

    // Credit status 
    'CREDIT_STATUS_ALL' => [
        'A' => 'Active',
        'I' => 'Inactive',
        //'D' => 'Deleted',
        // 'Approved' => 'Approved',
        // 'Rejected' => 'Rejected'
    ],

    //PAYMENT TERMS
    'PAYMENT_TERMS' => [
        'One month Credit at End of Month' => 'One month Credit at End of Month',
        'Monthly EMI for 6 Months' => 'Monthly EMI for 6 Months',
        'Monthly EMI for 12 Months' => 'Monthly EMI for 12 Months',
    ],

    //Loyalty value converted to FCFA for 1 unit
    'REDEEM_LOYALTY' => [
        'Standard' => '1.00',
        'VIP' => '2.00'
    ],

    // Approval status 
    'APPROVAL_STATUS' => [
        'Pending' => 'Pending',
        'Approved' => 'Approve',
        'Rejected' => 'Reject'
    ],

    // Product Preferences
    'PRODUCT_PREFERNECES' => [
        'Deal' => 'Deal of the day',
        'Featured' => 'Featured'
    ],

    //File Paths
    'PRODUCT_BAR_CODE' => "products/barcode/",
    'PRODUCT_QR_CODE' => "products/qrcode/",

    'VARIANT_SIZES' => [
        'Very Small' => 'Very Small',
        'Small' => 'Small',
        'Medium' => 'Medium',
        'Large' => 'Large',
        'Very Large' => 'Very Large',
    ],

    'MANAGER_ROLE_ID' => 32,

    'ABIDJAN_CITY_ID' => 2,

    //File Paths
    'COMING_SOON_WEB_IMAGE' => "COMING_SOON/web_image/",
    'COMING_SOON_MOB_IMAGE' => "COMING_SOON/mob_image/",

    //Image dimensions
    'COMING_SOON_WEB_IMAGE_MIN_WIDTH' => 240,
    'COMING_SOON_WEB_IMAGE_MAX_WIDTH' => 320,
    'COMING_SOON_WEB_IMAGE_MIN_HEIGHT' => 180,
    'COMING_SOON_WEB_IMAGE_MAX_HEIGHT' => 250,

    'COMING_SOON_MOB_IMAGE_MIN_WIDTH' => 170,
    'COMING_SOON_MOB_IMAGE_MAX_WIDTH' => 220,
    'COMING_SOON_MOB_IMAGE_MIN_HEIGHT' => 100,
    'COMING_SOON_MOB_IMAGE_MAX_HEIGHT' => 140,

    //Allowed Image Types
    'COMING_SOON_WEB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'COMING_SOON_WEB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'COMING_SOON_WEB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],
    'COMING_SOON_MOB_IMAGE_TYPE' => ["jpg", "jpeg", "png", "svg"],
    'COMING_SOON_MOB_IMAGE_TYPE_DISP' => [".jpg", ".jpeg", ".png", ".svg"],
    'COMING_SOON_MOB_IMAGE_JS_TYPE' => ["image/jpg", "image/jpeg", "image/png", "image/svg"],

    //MAX Allowed Size pf Images in mb
    'COMING_SOON_WEB_IMAGE_SIZE' => 10,
    'COMING_SOON_MOB_IMAGE_SIZE' => 5,

    'SHOWROOM_MANAGER_ROLE_ID' => 23,
    'SHOWROOM_SUPERVISOR_ROLE_ID' => 24,

    'SALES_PERSON_ROLE_ID' => 27,

    'ORDER_STATUS_PROGRESS_COLOR' => [
        'Pending'               => 'l-bg-orange',
        'Processing'            => 'l-bg-orange-dark',
        'Shipped'               => 'l-bg-green',
        'Partially Delivered'   => 'l-bg-green',
        'Delivered'             => 'l-bg-green-dark',
        'Cancelled'             => 'l-bg-red',
        'Returned'              => 'l-bg-red-dark',
        'Pending Cancellation'  => 'l-bg-cyan',
        'Cancellation Rejected' => 'l-bg-cyan-dark',
        'Pending Return'        => 'l-bg-cyan',
        'Return Approved'        => 'l-bg-cyan-dark',
        'Return Rejected'        => 'l-bg-cyan-dark',
        'Expired'               => 'l-bg-purple',
        'Deleted'               => 'l-bg-purple-dark',
    ],

    'ORDER_STATUS_PROGRESS_BAR' => [
        'Pending'               => '10%',
        'Processing'            => '30%',
        'Shipped'               => '50%',
        'Partially Delivered'   => '70%',
        'Delivered'             => '100%',
        'Cancelled'             => '100%',
        'Returned'              => '80%',
        'Pending Cancellation'  => '40%',
        'Cancellation Rejected' => '100%',
        'Pending Return'        => '60%',
        'Return Approved'        => '100%',
        'Return Rejected'        => '100%',
        'Expired'               => '100%',
        'Deleted'               => '100%'
    ],


    'EXP_PAYMENT_STATUSES' => [
        'Paid' => 'Paid',
        'Unpaid' => 'Unpaid'
    ],

    'EXP_PAYMENT_STATUSES_MAP' => [
        'Paid' => ['label' => 'Paid', 'class' => 'col-green'],
        'Unpaid' => ['label' => 'Unpaid', 'class' => 'col-red']
    ],

    'PAYMENT_STATUS_PROGRESS_COLOR' => [
        'Pending'               => 'l-bg-orange',
        'Completed'            => 'l-bg-orange-dark',
        'Failed'               => 'l-bg-red',
        'Refunded'             => 'l-bg-green-dark',
        'Paid'                 => 'l-bg-green',
        'Unpaid'                => 'l-bg-red-dark',
    ],

    'PAYMENT_STATUS_PROGRESS_BAR' => [
        'Pending'               => '10%',
        'Completed'            => '90%',
        'Failed'               => '0%',
        'Refunded'             => '100%',
        'Paid'                 => '100%',
        'Unpaid'              => '50%',
    ],

    'SHIPPED_STATUS_PROGRESS_COLOR' => [
        'Pending'    => 'l-bg-orange',
        'Processing' => 'l-bg-orange-dark',
        'In Transit' => 'l-bg-blue',
        'Delivered'  => 'l-bg-green',
        'Cancelled'  => 'l-bg-red',
        'Failed'     => 'l-bg-dark',
        'Returned'   => 'l-bg-purple',
    ],

    'SHIPPED_STATUS_PROGRESS_BAR' => [
        'Pending'    => '5%',
        'Processing' => '30%',
        'In Transit' => '60%',
        'Delivered'  => '100%',
        'Cancelled'  => '0%',
        'Failed'     => '0%',
        'Returned'   => '10%',
    ],

    'DELIVERY_STATUS_PROGRESS_COLOR' => [
        'Not_Started'        => 'l-bg-orange',
        'Out_For_Delivery'   => 'l-bg-blue',
        'Partially_Delivered' => 'l-bg-purple',
        'Delivered'          => 'l-bg-green',
        'Failed'             => 'l-bg-red',
        'Returned'           => 'l-bg-orange-dark',
    ],

    'DELIVERY_STATUS_PROGRESS_BAR' => [
        'Not_Started'        => '10%',
        'Out_For_Delivery'   => '50%',
        'Partially_Delivered' => '70%',
        'Delivered'          => '100%',
        'Failed'             => '30%',
        'Returned'           => '0%',
    ],

    'TEMP_IMAGE_FOLDER' => 'uploads/temp_images/'
];
